"""
Common models for MeOS Python SDK.

This module contains commonly used models across different API operations.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from pydantic import Field

from .base import BaseModel, BaseResponse, TreeNode


class CodeNamePair(BaseModel):
    """Code and name pair model."""

    code: str = Field(..., description="编码")
    name: str = Field(..., description="名称")


class InstanceTreeNode(TreeNode[Dict[str, Any]]):
    """Instance tree node model."""

    dataCode: str = Field(..., description="数据编码")
    insName: str = Field(..., description="实例名称")
    nodeType: str = Field(..., description="节点类型")
    modelCode: Optional[str] = Field(None, description="模型编码")
    parentCode: Optional[str] = Field(None, description="父级编码")
    children: List["InstanceTreeNode"] = Field(default_factory=list, description="子数据列表")


class PropertyInfo(BaseModel):
    """Property information model."""

    propertyCode: str = Field(..., description="属性编码")
    propertyName: str = Field(..., description="属性名称")
    dataType: str = Field(..., description="数据类型")
    unit: Optional[str] = Field(None, description="单位")
    description: Optional[str] = Field(None, description="描述")
    isRequired: bool = Field(False, description="是否必填")
    defaultValue: Optional[Union[str, int, float, bool]] = Field(None, description="默认值")
    minValue: Optional[Union[int, float]] = Field(None, description="最小值")
    maxValue: Optional[Union[int, float]] = Field(None, description="最大值")
    enumValues: Optional[List[str]] = Field(None, description="枚举值")


class StaticPropertyValue(BaseModel):
    """Static property value model."""

    propertyCode: str = Field(..., description="属性编码")
    propertyName: Optional[str] = Field(None, description="属性名称")
    value: Optional[Union[str, int, float, bool]] = Field(None, description="属性值")
    unit: Optional[str] = Field(None, description="单位")
    dataType: Optional[str] = Field(None, description="数据类型")


class DynamicPropertyValue(BaseModel):
    """Dynamic property value model."""

    dataCode: str = Field(..., description="数据编码")
    propertyCode: str = Field(..., description="属性编码")
    propertyName: Optional[str] = Field(None, description="属性名称")
    value: Optional[Union[str, int, float, bool]] = Field(None, description="属性值")
    unit: Optional[str] = Field(None, description="单位")
    dataType: Optional[str] = Field(None, description="数据类型")
    timestamp: Optional[datetime] = Field(None, description="时间戳")
    quality: Optional[str] = Field(None, description="数据质量")

    @property
    def isGoodQuality(self) -> bool:
        """Check if the data quality is good."""
        return self.quality == "GOOD" if self.quality else True


class TimeRange(BaseModel):
    """Time range model."""

    startTime: datetime = Field(..., description="开始时间")
    endTime: datetime = Field(..., description="结束时间")

    def __post_init__(self) -> None:
        """Validate time range."""
        if self.startTime >= self.endTime:
            raise ValueError("startTime must be before endTime")

    @property
    def durationSeconds(self) -> float:
        """Get duration in seconds."""
        return (self.endTime - self.startTime).total_seconds()

    @property
    def durationMinutes(self) -> float:
        """Get duration in minutes."""
        return self.durationSeconds / 60

    @property
    def durationHours(self) -> float:
        """Get duration in hours."""
        return self.durationSeconds / 3600


class PaginationRequest(BaseModel):
    """Pagination request model."""

    pageNumber: int = Field(1, ge=1, description="页码")
    pageSize: int = Field(10, ge=1, le=1000, description="每页大小")

    @property
    def offset(self) -> int:
        """Calculate offset for database queries."""
        return (self.pageNumber - 1) * self.pageSize


class SortOrder(BaseModel):
    """Sort order model."""

    field: str = Field(..., description="排序字段")
    direction: str = Field("asc", pattern="^(asc|desc)$", description="排序方向")

    @property
    def isAscending(self) -> bool:
        """Check if sort direction is ascending."""
        return self.direction.lower() == "asc"

    @property
    def isDescending(self) -> bool:
        """Check if sort direction is descending."""
        return self.direction.lower() == "desc"


class QueryFilter(BaseModel):
    """Query filter model."""

    field: str = Field(..., description="过滤字段")
    operator: str = Field(..., description="操作符")
    value: Union[str, int, float, bool, List[Any]] = Field(..., description="过滤值")

    # Common operators
    EQUALS: str = "eq"
    NOT_EQUALS: str = "ne"
    GREATER_THAN: str = "gt"
    GREATER_THAN_OR_EQUAL: str = "gte"
    LESS_THAN: str = "lt"
    LESS_THAN_OR_EQUAL: str = "lte"
    IN: str = "in"
    NOT_IN: str = "not_in"
    LIKE: str = "like"
    NOT_LIKE: str = "not_like"
    IS_NULL: str = "is_null"
    IS_NOT_NULL: str = "is_not_null"


class SearchRequest(BaseModel):
    """Search request model."""

    keyword: Optional[str] = Field(None, description="搜索关键词")
    filters: List[QueryFilter] = Field(default_factory=list, description="过滤条件")
    sort: List[SortOrder] = Field(default_factory=list, description="排序条件")
    pagination: PaginationRequest = Field(default_factory=PaginationRequest, description="分页参数")


class BulkOperationResult(BaseModel):
    """Bulk operation result model."""

    total: int = Field(..., description="总数")
    success: int = Field(..., description="成功数")
    failed: int = Field(..., description="失败数")
    errors: List[str] = Field(default_factory=list, description="错误信息")

    @property
    def successRate(self) -> float:
        """Calculate success rate."""
        return self.success / self.total if self.total > 0 else 0.0

    @property
    def isAllSuccess(self) -> bool:
        """Check if all operations succeeded."""
        return self.failed == 0

    @property
    def hasErrors(self) -> bool:
        """Check if there are any errors."""
        return self.failed > 0


class ValTimes(BaseModel):
    """时间和值模型 (OpenAPI: ValTimes)"""

    propVal: str = Field(..., description="属性值")
    timestamp: str = Field(..., description="时间戳")


class DataIntervalQueryVo(BaseModel):
    """等时间间隔数据项 (OpenAPI: DataIntervalQueryVo)"""

    dataCode: str = Field(..., description="属性编码")
    valTimes: List[ValTimes] = Field(..., description="时间和值列表")


class DataPointQueryVo(BaseModel):
    """时间区间/断面数据项 (OpenAPI: DataPointQueryVo)"""

    dataCode: str = Field(..., description="属性编码")
    propVal: str = Field(..., description="属性值")
    timestamp: str = Field(..., description="时间戳")


class EquallySpacedTimeDataRequest(BaseModel):
    """获取历史数据等时间间隔采样数据请求 (OpenAPI: 请求等时间间隔数据接口)"""

    dataCodes: List[str] = Field(..., description="属性编码列表")
    startTime: str = Field(..., description="开始时间开始时间,示例值:2024-08-08 10:00:00")
    endTime: str = Field(..., description="结束时间,示例值:2024-08-08 10:00:00")
    interval: int = Field(..., description="步长")
    tsUnit: str = Field(..., description="步长单位:(Y:年;M:月;D:日;H:小时;MIN:分),示例值(D)")


class TimeIntervalDataRequest(BaseModel):
    """获取时间区间数据请求 (OpenAPI: 获取时间区间数据DTO)"""

    dataCodes: List[str] = Field(..., description="属性编码列表")
    startTime: str = Field(..., description="开始时间,示例值:2024-08-08 10:00:00")
    endTime: str = Field(..., description="结束时间,示例值:2024-08-08 11:00:00")


class TimeSliceDataRequest(BaseModel):
    """获取指定时间断面数据请求 (OpenAPI: 请求时间截面数据接口)"""

    dataCodes: List[str] = Field(..., description="属性编码列表")
    ts: Optional[str] = Field(None, description="时间,示例值:2024-08-08 10:00:00,如果不传返回最新值")


class TimeoutConfig(BaseModel):
    connect: int = Field(..., description="连接超时时间")
    send: int = Field(..., description="发送超时时间")
    read: int = Field(..., description="接收超时时间")


class NodesConfig(BaseModel):
    # 动态key: IP:PORT, value: 权重
    # 这里用Dict[str, int], 但Field描述要说明
    __root__: Dict[str, int] = Field(..., description="服务节点配置(IP:PORT为key, value为权重)")


class Upstream(BaseModel):
    type: str = Field(..., description="负载均衡类型, 默认 roundrobin")
    pass_host: str = Field(..., description="主机转发方式, 默认 pass")
    scheme: str = Field(..., description="协议 [http, https, grpc, grpcs, tcp, udp]")
    timeout: Optional[TimeoutConfig] = Field(None, description="超时配置")
    nodes: Dict[str, int] = Field(..., description="服务节点配置(IP:PORT为key, value为权重)")


class RouteRequest(BaseModel):
    name: str = Field(..., description="路由名称")
    desc: Optional[str] = Field("", description="路由描述")
    status: int = Field(..., description="路由状态(0下线; 1上线)")
    methods: List[str] = Field(..., description="HTTP 方法列表")
    uri: str = Field(..., description="HTTP 请求路径")
    upstream: Upstream = Field(..., description="映射节点配置")


class RouteIdResponse(BaseModel):
    id: str = Field(..., description="路由ID")


class RouteResponse(BaseResponse[RouteIdResponse]):
    """注册/修改路由响应"""

    pass


class RouteInfo(BaseModel):
    id: str = Field(..., description="路由ID")
    name: str = Field(..., description="路由名称")
    desc: str = Field(..., description="路由描述")
    status: int = Field(..., description="路由状态(0 下线 1上线)")
    methods: List[str] = Field(..., description="HTTP 方法列表")
    uri: str = Field(..., description="HTTP 请求路径")
    upstream: Upstream = Field(..., description="映射节点配置")


class GetRouteResponse(BaseResponse[RouteInfo]):
    """查询路由响应"""

    pass


class SimpleResponse(BaseResponse[Any]):
    """通用响应(如删除、上下线)"""

    pass


class MessageItem(BaseModel):
    """消息内容项 (OpenAPI: messages array item)"""

    is_delay: bool = Field(..., description="是否延迟消息, true:是延迟消息；false:不是延迟消息")
    delay_start_time: Optional[str] = Field(None, description="延迟消息开始时间, 延迟消息必须, 例如: 2023-01-01 01:01:01")
    delay_time: Optional[int] = Field(None, description="延迟时间/秒, 延迟消息必须")
    message_id: Optional[str] = Field(None, description="消息ID, 用于追溯消息")
    content: Dict = Field(..., description='消息内容, 例如: {"name":"张三","age":18,"hobby":["study","sport"]}')


class MessageSendRequest(BaseModel):
    """消息发送请求体 (OpenAPI: /p/mbus/message/send)"""

    topic_id: str = Field(..., description="消息主题调用ID, 从消息总线【消息主题管理】->【消息主题管理】->【Tpopic调用ID】")
    is_forward: str = Field(
        ...,
        description="是否转发消息, true:消息总线会使用内部机制将消息转发给消费者；false:消息总线只将消息发送到对应的中间件, 不做转发, 默认true",
    )
    messages: List[MessageItem] = Field(..., description="消息内容列表, 可多条")


class DwDeviceDataExtractionRequest(BaseModel):
    """单项目下任意设备数据获取请求体 (OpenAPI: /p/dw/dw_common/data-extraction)"""

    projectCode: str = Field(..., description="项目编码【数字孪生的长码】")
    interval: str = Field(..., description="间隔时间, 分钟级: 1m  小时级: 1h 日级: 1d 月级: 1n")
    stime: str = Field(..., description="开始时间, 格式: yyyy-MM-DD HH:mm:ss")
    etime: str = Field(..., description="结束时间, 格式: yyyy-MM-DD HH:mm:ss")
    sysCode: str = Field(..., description="系统编码, 数字孪生的长码")
    stationCode: str = Field(..., description="站编码, 数字孪生的长码")
    unitCode: str = Field(..., description="单元编码, 数字孪生的长码")
    devCode: str = Field(..., description="设备编码, 数字孪生的长码")
    current: int = Field(..., description="当前页, 1开始")
    size: int = Field(..., description="每页条数")


class DwDeviceDataExtractionItem(BaseModel):
    ts: str = Field(..., description="时间")
    projectCode: str = Field(..., description="项目编码")
    projectName: str = Field(..., description="项目名称")
    systemCode: str = Field(..., description="系统编码")
    systemName: str = Field(..., description="系统名称")
    stationCode: Optional[str] = Field(None, description="站编码")
    stationName: Optional[str] = Field(None, description="站名称")
    unitCode: Optional[str] = Field(None, description="单元编码")
    unitName: Optional[str] = Field(None, description="单元名称")
    deviceCode: str = Field(..., description="设备编码")
    deviceName: str = Field(..., description="设备名称")
    propCode: str = Field(..., description="属性编码")
    propName: str = Field(..., description="属性名称")
    itemid: str = Field(..., description="采集点编码")
    gatewayCode: str = Field(..., description="网关编号")
    itemValue: float = Field(..., description="采集值")
    tsFormat: str = Field(..., description="时间戳")


class DwDeviceDataExtractionResponseData(BaseModel):
    total: int = Field(..., description="总条数")
    list: List[DwDeviceDataExtractionItem] = Field(..., description="数据列表")


class DwDeviceDataExtractionResponse(BaseResponse[DwDeviceDataExtractionResponseData]):
    """单项目下任意设备数据获取响应体"""

    pass


class DeviceControlItem(BaseModel):
    """设备控制实体类 (OpenAPI: 设备控制实体类)"""

    propCode: str = Field(..., description="设备属性编码")
    targetValue: str = Field(..., description="目标值")


class ControlCommandDetail(BaseModel):
    """控制指令详情 (OpenAPI: 控制指令详情)"""

    issueDetail: List[DeviceControlItem] = Field(..., description="命令详情")
    userAccount: str = Field(..., description="用户")


class ControlCommandResponse(BaseResponse[Dict]):
    """下发控制命令响应 (OpenAPI: 响应)"""

    pass


class FeedbackValueItem(BaseModel):
    """属性值实体类 (OpenAPI: 属性值实体类)"""

    dataCode: str = Field(..., description="设备属性编码")
    val: str = Field(..., description="值")


class FeedbackValueResponse(BaseResponse[Optional[List[FeedbackValueItem]]]):
    """数据实时值查询响应 (OpenAPI: 响应«List«属性值实体类»»)"""

    pass


class RuleItem(BaseModel):
    """规则项 (OpenAPI: 规则库相关接口data.list项)"""

    id: Optional[str] = Field(None, description="规则ID")
    sysName: Optional[str] = Field(None, description="系统名称")
    stationName: Optional[str] = Field(None, description="站名称")
    deviceName: Optional[str] = Field(None, description="设备名称")
    propName: Optional[str] = Field(None, description="属性名称")
    # 可根据实际OpenAPI补充字段


class RuleListResponseData(BaseModel):
    total: Optional[int] = Field(None, description="总条数")
    list: Optional[List[RuleItem]] = Field(None, description="规则项列表")


class RuleListResponse(BaseResponse[RuleListResponseData]):
    """规则库分页/列表响应"""

    pass


class FindBySysNameRequest(BaseModel):
    """根据系统名称模糊查询规则请求"""

    sysName: str = Field(..., description="系统名称")
    page: int = Field(..., description="页数")
    size: int = Field(..., description="页容量")


class FindBySysNameAndStationNameRequest(BaseModel):
    """根据系统名称和站名称模糊查询请求"""

    sysName: str = Field(..., description="系统名称")
    stationName: str = Field(..., description="站名称")
    page: str = Field(..., description="页码")
    size: str = Field(..., description="页容量")


class FindExactBySysNameAndStationNameRequest(BaseModel):
    """根据系统名称和站名称精确查询请求"""

    sysName: str = Field(..., description="系统名称")
    stationName: str = Field(..., description="站名称")
    page: str = Field(..., description="页码")
    size: str = Field(..., description="页容量")


class FindByDeviceNameRequest(BaseModel):
    """根据设备名称模糊查询请求"""

    deviceName: str = Field(..., description="设备名称")
    page: str = Field(..., description="页码")
    size: str = Field(..., description="页容量")


class FindByConditionsRequest(BaseModel):
    """综合查询，支持多个字段模糊组合查询请求"""

    deviceName: str = Field(..., description="设备模型名称")
    propName: str = Field(..., description="属性名称")
    stationName: Optional[str] = Field(None, description="站模型名称")
    sysName: Optional[str] = Field(None, description="系统模型名称")
    page: str = Field(..., description="页码")
    size: str = Field(..., description="每页数据量")


class RuleRecord(BaseModel):
    id: int = Field(..., description="规则ID")
    deviceName: str = Field(..., description="设备模型名称")
    deviceCode: str = Field(..., description="设备模型编码")
    sysName: str = Field(..., description="系统模型名称")
    sysCode: str = Field(..., description="系统模型编码")
    stationName: str = Field(..., description="站模型名称")
    stationCode: str = Field(..., description="站模型编码")
    propName: str = Field(..., description="属性名称")
    propCode: str = Field(..., description="属性编码")
    dataCode: str = Field(..., description="属性短码")
    unit: Optional[str] = Field(None, description="单位")
    constraintType: int = Field(..., description="约束类型")
    constraintExpress: Optional[str] = Field(None, description="约束表达式")


class RuleListPageResponseData(BaseModel):
    records: list[RuleRecord] = Field(..., description="记录")
    total: int = Field(..., description="总计")
    size: int = Field(..., description="页容量")
    current: int = Field(..., description="当前页")
    orders: list[str] = Field(..., description="排序")
    optimizeCountSql: bool = Field(..., description="是否优化统计SQL")
    searchCount: bool = Field(..., description="是否统计总数")
    countId: Optional[str] = Field(None, description="计数ID")
    maxLimit: Optional[int] = Field(None, description="最大限制")
    pages: int = Field(..., description="总页数")


class FindBySysNameResponse(BaseResponse[RuleListPageResponseData]):
    """根据系统名称模糊查询规则响应"""

    pass


class FindBySysNameAndStationNameResponse(BaseResponse[RuleListPageResponseData]):
    """根据系统名称和站名称模糊查询响应"""

    pass


class FindExactBySysNameAndStationNameResponse(BaseResponse[RuleListPageResponseData]):
    """根据系统名称和站名称精确查询响应"""

    pass


class FindByDeviceNameResponse(BaseResponse[RuleListPageResponseData]):
    """根据设备名称模糊查询响应"""

    pass


class FindByConditionsResponse(BaseResponse[RuleListPageResponseData]):
    """综合查询，支持多个字段模糊组合查询响应"""

    pass


class EquallySpacedTimeDataResponse(BaseResponse[List[DataIntervalQueryVo]]):
    """获取历史数据等时间间隔采样数据响应 (OpenAPI: data: List[DataIntervalQueryVo])"""

    pass


class TimeIntervalDataResponse(BaseResponse[List[DataPointQueryVo]]):
    """获取时间区间数据响应 (OpenAPI: data: List[DataPointQueryVo])"""

    pass


class TimeSliceDataResponse(BaseResponse[List[DataPointQueryVo]]):
    """获取指定时间断面数据响应 (OpenAPI: data: List[DataPointQueryVo])"""

    pass
