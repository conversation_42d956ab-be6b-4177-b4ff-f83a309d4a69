"""
Project instance service for MeOS Python SDK.

This module provides project instance operations.
Tag: 项目实例信息
"""

import logging
from typing import Optional

from meos.services.common.attribute_service import CommonAttributeServiceMixin
from meos.services.common.instance_info_service import CommonInstanceServiceMixin

from ..models.instances import (
    CommonBaseInfoListRequest,
    CommonBaseInfoListResponse,
    CommonDynamicInfoListRequest,
    CommonDynamicPropertyListResponse,
    CommonStaticInfoListRequest,
    CommonStaticPropertyListResponse,
    ProjectInstanceListResponse,
    ProjectInstanceResponse,
    ProjectInstanceTreeResponse,
)

logger = logging.getLogger(__name__)


class ProjectInstanceService(CommonAttributeServiceMixin, CommonInstanceServiceMixin):
    """Project instance service for MeOS API - 项目实例信息."""

    PROJECT_INFO_ENDPOINT = "/p/dt/v1/ins/project/info"
    PROJECT_LIST_ENDPOINT = "/p/dt/v1/ins/project/ins/list"
    PROJECT_TREE_ENDPOINT = "/p/dt/v1/ins/project/ins/tree"
    BASE_PROPERTIES_ENDPOINT = "/p/dt/v1/ins/project/page/base"
    DYNAMIC_PROPERTIES_ENDPOINT = "/p/dt/v1/ins/project/page/dynamic"
    STATIC_PROPERTIES_ENDPOINT = "/p/dt/v1/ins/project/page/static"

    def get_project_info(self, dataCode: Optional[str] = None) -> ProjectInstanceResponse:
        """
        获取项目实例详细信息.
        Args:
            dataCode: 项目数据编码
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """

        return self.get_instance_info(
            self.PROJECT_INFO_ENDPOINT, dataCode=dataCode, response_model=ProjectInstanceResponse
        )

    async def get_project_info_async(self, dataCode: Optional[str] = None) -> ProjectInstanceResponse:
        """
        获取项目实例详细信息 (异步).

        Args:
            dataCode: 项目数据编码

        Returns:
            完整的API响应, 包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """

        return await self.get_instance_info_async(
            self.PROJECT_INFO_ENDPOINT, dataCode=dataCode, response_model=ProjectInstanceResponse
        )

    def list_project_instances(self) -> ProjectInstanceListResponse:
        """
        查询项目实例列表.

        Returns:
            完整的API响应, 包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        response = self._make_request(
            method="GET",
            url=self.PROJECT_LIST_ENDPOINT,
            response_model=ProjectInstanceListResponse,
        )

        return response

    async def list_project_instances_async(self) -> ProjectInstanceListResponse:
        """
        查询项目实例列表 (异步).

        Returns:
            完整的API响应, 包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        response = await self._make_async_request(
            method="GET",
            url=self.PROJECT_LIST_ENDPOINT,
            response_model=ProjectInstanceListResponse,
        )

        return response

    def get_project_tree(self, dataCode: str) -> ProjectInstanceTreeResponse:
        """
        查询项目实例所有下级 (树) .

        Args:
            dataCode: 项目数据编码
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(dataCode=dataCode)
        response = self._make_request(
            method="GET",
            url=self.PROJECT_TREE_ENDPOINT,
            response_model=ProjectInstanceTreeResponse,
            params=params,
        )
        return response

    async def get_project_tree_async(self, data_code: str) -> ProjectInstanceTreeResponse:
        """
        查询项目实例所有下级 (树) (异步).

        Args:
            data_code: 项目数据编码

        Returns:
            完整的API响应, 包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(dataCode=data_code)

        response = await self._make_async_request(
            method="GET",
            url=self.PROJECT_TREE_ENDPOINT,
            response_model=ProjectInstanceTreeResponse,
            params=params,
        )

        return response

    def get_base_properties(
        self,
        req: CommonBaseInfoListRequest,
    ) -> CommonBaseInfoListResponse:
        """
        获取基础属性列表.

        Args:
            req: CommonBaseInfoListRequest
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """

        return self.get_common_base_properties(self.BASE_PROPERTIES_ENDPOINT, req)

    async def get_base_properties_async(
        self,
        req: CommonBaseInfoListRequest,
    ) -> CommonBaseInfoListResponse:
        """
        获取基础属性列表 (异步).

        Args:
            req: CommonBaseInfoListRequest

        Returns:
            完整的API响应, 包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """

        return await self.get_common_base_properties_async(self.BASE_PROPERTIES_ENDPOINT, req)

    def get_dynamic_properties(
        self,
        req: CommonDynamicInfoListRequest,
    ) -> CommonDynamicPropertyListResponse:
        """
        获取动态属性列表.

        Args:
            req: CommonDynamicInfoListRequest
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """

        return self.get_common_dynamic_properties(self.DYNAMIC_PROPERTIES_ENDPOINT, req)

    async def get_dynamic_properties_async(
        self,
        req: CommonDynamicInfoListRequest,
    ) -> CommonDynamicPropertyListResponse:
        """
        获取动态属性列表 (异步).

        Args:
            req: CommonDynamicInfoListRequest

        Returns:
            完整的API响应, 包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """

        return await self.get_common_dynamic_properties_async(self.DYNAMIC_PROPERTIES_ENDPOINT, req)

    def get_static_properties(
        self,
        req: CommonStaticInfoListRequest,
    ) -> CommonStaticPropertyListResponse:
        """
        获取静态属性列表.

        Args:
            req: CommonStaticInfoListRequest
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """

        return self.get_common_static_properties(self.STATIC_PROPERTIES_ENDPOINT, req)

    async def get_static_properties_async(
        self,
        req: CommonStaticInfoListRequest,
    ) -> CommonStaticPropertyListResponse:
        """
        获取静态属性列表 (异步).

        Args:
            req: CommonStaticInfoListRequest

        Returns:
            完整的API响应, 包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """

        return await self.get_common_static_properties_async(self.STATIC_PROPERTIES_ENDPOINT, req)
